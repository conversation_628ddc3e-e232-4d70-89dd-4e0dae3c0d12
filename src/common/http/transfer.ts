import { AxiosRequestConfig } from 'axios';
import { axiosInstance, GATEWAY_PATH } from '.';

interface TransferRPC {
  psm: string;
  method: string;
  requestParams: any;
}

interface TransferHTTP {
  path: string;
  requestParams: any;
}

export const transferRPC = async (
  args: TransferRPC,
  axiosOptions?: AxiosRequestConfig,
): Promise<any> =>
  axiosInstance({
    url: `${GATEWAY_PATH}?request_psm=${args.psm}&request_method=${args.method}`,
    method: 'POST',
    data: args.requestParams,
    ...axiosOptions,
  });

export const transferHTTP = async (
  args: TransferHTTP,
  axiosOptions?: AxiosRequestConfig,
): Promise<any> =>
  axiosInstance({
    url: `${GATEWAY_PATH}?path=${args.path}`,
    method: 'POST',
    data: args.requestParams,
    ...axiosOptions,
  });
