import { FC, useMemo } from 'react';
import { Table, Typography } from '@hi-design/ui';
import { format } from 'date-fns';

import styles from './index.module.scss';
import classNames from 'classnames';
import { ArgosLogLevel } from '@/common/constants/info';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';

export interface ILogRendererProps {
  logs: info_service.LogEntry[];
}

interface GroupedLog {
  psm: string;
  timeStamp: string;
  pod: string;
  logs: info_service.LogEntry[];
}

export const LogRenderer: FC<ILogRendererProps> = props => {
  const { logs } = props;

  // Group logs by PSM
  const groupedLogs = useMemo(() => {
    const groups: Record<string, info_service.LogEntry[]> = {};

    logs.forEach(log => {
      if (!groups[log.psm || 'No PSM']) {
        groups[log.psm || 'No PSM'] = [];
      }
      groups[log.psm || 'No PSM'].push(log);
    });

    // Sort each group by timestamp
    Object.keys(groups).forEach(psm => {
      groups[psm].sort(
        (a: info_service.LogEntry, b: info_service.LogEntry) =>
          Number(a.timeStamp) - Number(b.timeStamp),
      );
    });

    // Convert to array format for table
    return Object.keys(groups)
      .map(psm => {
        const logsForPsm = groups[psm];
        const earliestLog = logsForPsm[0];

        return {
          psm: earliestLog.psm || 'No PSM',
          timeStamp: earliestLog.timeStamp || '',
          pod: earliestLog.pod || '',
          logs: logsForPsm,
        };
      })
      .sort(
        (a: GroupedLog, b: GroupedLog) =>
          Number(a.timeStamp) - Number(b.timeStamp),
      );
  }, [logs]);

  // Format timestamp to human-readable format
  const formatTimestamp = (timestamp: string): string => {
    if (!timestamp) {
      return '-';
    }
    try {
      return format(
        new Date(Number(timestamp) / 1000),
        'yyyy-MM-dd HH:mm:ss.SSS',
      );
    } catch (e) {
      return timestamp;
    }
  };

  // Render expanded content for each row
  const renderRowContent = (record: GroupedLog) => (
    <div className={styles.expandedContent}>
      {record.logs.map((log: info_service.LogEntry) => {
        // Use a more unique key by combining timestamp and a portion of the log message
        const KEY_LENGTH = 20;
        const uniqueKey = `${log.timeStamp}-${(log.logMsg || '').substring(0, KEY_LENGTH)}`;

        return (
          <div
            key={uniqueKey}
            className={classNames(styles.logEntry, {
              [styles.error]: log.level === ArgosLogLevel.ERROR,
              [styles.warn]: log.level === ArgosLogLevel.WARN,
              [styles.info]: log.level === ArgosLogLevel.INFO,
            })}
          >
            <Typography.Text type="secondary">
              {`${log.level || '-'} ${formatTimestamp(log.timeStamp || '')} ${log.location || '-'} ipv4=${log.ip || '-'} psm=${log.psm || '-'} logId=${log.logId || '-'} IDC=${log.idc || '-'} pod=${log.pod || '-'} isPPE=${log.ifPpe ? 'true' : 'false'} spanID=${log.spanId || '-'}`}
            </Typography.Text>
            <Typography.Text type="secondary">{`message=${log.logMsg || '-'}`}</Typography.Text>
          </div>
        );
      })}
    </div>
  );

  const columns = [
    {
      title: 'PSM',
      dataIndex: 'psm',
      key: 'psm',
      render: (text: string) => (
        <Typography.Text type="danger">{text}</Typography.Text>
      ),
    },
    {
      title: 'TimeStamp',
      dataIndex: 'timeStamp',
      key: 'timeStamp',
      render: (text: string) => (
        <Typography.Text type="tertiary">
          {formatTimestamp(text)}
        </Typography.Text>
      ),
    },
    {
      title: 'Pod',
      dataIndex: 'pod',
      key: 'pod',
      render: (text: string) => (
        <Typography.Text type="tertiary" copyable>
          {text}
        </Typography.Text>
      ),
    },
  ];

  return (
    <div className={styles.LogRenderer}>
      <Table
        expandRowByClick
        columns={columns}
        dataSource={groupedLogs}
        rowKey="psm"
        sticky
        headerBgColorType="grey"
        pagination={false}
        expandedRowRender={renderRowContent}
        className={styles.logTable}
      />
    </div>
  );
};
