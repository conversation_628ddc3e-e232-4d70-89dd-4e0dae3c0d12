import axios, { AxiosInstance, AxiosResponse } from 'axios';

export const SYSTEM_ERROR_CODE = -1;
export const SYSTEM_ERROR_MESSAGE = 'Network Error';

export const GATEWAY_PATH = '/gateway/request/transfer';

// Error codes
export const INVALID_RESPONSE_CODE = -1;
export const GATEWAY_ERROR_CODE = -2;
export const NETWORK_ERROR_CODE = 0;
export const CANCELLED_REQUEST_CODE = -3;
export const INVALID_RESPONSE_MESSAGE = 'Invalid response body';
export const GATEWAY_ERROR_MESSAGE = 'Gateway Error';
export const NETWORK_ERROR_MESSAGE = 'Network Error';
export const CANCELLED_REQUEST_MESSAGE = 'Request Cancelled';

// Gateway response status constants
export const GATEWAY_SUCCESS_STATUS = '0';
export const GATEWAY_SUCCESS_MESSAGE = 'success';

// Interface for error response
export interface ErrorResponse {
  statusCode: number;
  statusMsg: string;
  error: string;
  logId?: string;
  isCancelled?: boolean; // Flag to indicate if the request was cancelled
}

// Type guard to check if an error is an ErrorResponse
export function isErrorResponse(error: unknown): error is ErrorResponse {
  return (
    typeof error === 'object' &&
    error !== null &&
    'statusCode' in error &&
    'statusMsg' in error &&
    'error' in error
  );
}

// Helper function to check if an error response indicates a cancelled request
export function isRequestCancelled(error: unknown): boolean {
  if (isErrorResponse(error)) {
    return (
      error.isCancelled === true || error.statusCode === CANCELLED_REQUEST_CODE
    );
  }
  if (typeof error === 'object' && error !== null && 'ext' in error) {
    const systemError = error as SystemErrorResponse;
    return (
      systemError.ext?.isCancelled === true ||
      systemError.statusCode === CANCELLED_REQUEST_CODE
    );
  }
  return false;
}

// Interface for system error response
export interface SystemErrorResponse {
  statusCode: number;
  statusMsg: string;
  ext: {
    rawError: unknown;
    isCancelled?: boolean;
  };
}

/**
 * Helper function to handle gateway responses
 */
function handleGatewayResponse<T>(
  response: AxiosResponse,
  logId: string,
): Promise<T & { logId: string }> | ErrorResponse | Promise<never> {
  if (typeof response?.data !== 'object' || response?.data === null) {
    const errorObj: ErrorResponse = {
      statusCode: INVALID_RESPONSE_CODE,
      statusMsg: INVALID_RESPONSE_MESSAGE,
      error: String(response?.data),
      logId,
    };
    return Promise.reject(errorObj);
  }

  const responseData = response?.data;

  if (
    responseData?.status !== GATEWAY_SUCCESS_STATUS ||
    responseData?.message !== GATEWAY_SUCCESS_MESSAGE
  ) {
    const errorObj: ErrorResponse = {
      statusCode: GATEWAY_ERROR_CODE,
      statusMsg: GATEWAY_ERROR_MESSAGE,
      error: responseData?.message || '',
      logId,
    };
    return Promise.reject(errorObj);
  }

  const result = responseData?.data as T;

  // Return the result with logId added
  return Promise.resolve({
    ...result,
    logId,
  });
}

/**
 * Helper function to check if an error is a cancelled request
 */
function isCancelledRequest(err: unknown): boolean {
  if (axios.isAxiosError(err)) {
    return err.code === 'ERR_CANCELED' || err.message?.includes('canceled');
  }
  if (err instanceof Error) {
    return err.name === 'AbortError' || err.message?.includes('aborted');
  }
  return false;
}

/**
 * Helper function to handle gateway network errors
 */
function handleGatewayNetworkError(err: unknown): ErrorResponse {
  let errorMessage = 'Unknown error';
  const isCancelled = isCancelledRequest(err);

  // For cancelled requests, use a specific message
  if (isCancelled) {
    errorMessage = CANCELLED_REQUEST_MESSAGE;
  } else {
    // Try to extract status and statusText from axios error
    if (axios.isAxiosError(err) && err.response?.status) {
      errorMessage = `${err.response?.status} ${err.response?.statusText || ''}`;
    } else if (err instanceof Error && err?.message) {
      errorMessage = err.message;
    }
  }

  // Extract log ID if available (might be undefined for network errors)
  let logId: string | undefined;
  if (axios.isAxiosError(err) && err.response?.headers?.['x-tt-logid']) {
    logId = err.response?.headers?.['x-tt-logid'] as string;
  }

  return {
    statusCode: isCancelled ? CANCELLED_REQUEST_CODE : NETWORK_ERROR_CODE,
    statusMsg: isCancelled ? CANCELLED_REQUEST_MESSAGE : NETWORK_ERROR_MESSAGE,
    error: errorMessage,
    logId,
    isCancelled,
  };
}

// Create a configured axios instance
export const axiosInstance: AxiosInstance = axios.create();

// Request interceptor
axiosInstance.interceptors.request.use(
  config => config,
  error => Promise.reject(error),
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    const isGatewayRequest = response.config.url?.includes(GATEWAY_PATH);

    if (isGatewayRequest) {
      // Extract log ID from header
      const logId = (response?.headers?.['x-tt-logid'] as string) || '';
      return handleGatewayResponse(response, logId);
    }

    // For non-gateway requests, return response data
    return response.data;
  },
  err => {
    // If this is an error from our validation, pass it through
    if (isErrorResponse(err)) {
      return Promise.reject(err);
    }

    // Check if this is a cancelled request
    const isCancelled = isCancelledRequest(err);

    // Check if this is a gateway request
    const isGatewayRequest = err.config?.url?.includes(GATEWAY_PATH);

    if (isGatewayRequest) {
      // For gateway requests, create a network error response
      const errorResponse = handleGatewayNetworkError(err);
      return Promise.reject(errorResponse);
    }

    // For non-gateway requests, handle errors as before
    const errorResponse: SystemErrorResponse = {
      statusCode: isCancelled ? CANCELLED_REQUEST_CODE : SYSTEM_ERROR_CODE,
      statusMsg: isCancelled ? CANCELLED_REQUEST_MESSAGE : SYSTEM_ERROR_MESSAGE,
      ext: {
        rawError: err,
        isCancelled,
      },
    };
    return Promise.resolve(errorResponse);
  },
);
